{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=hl7storageai;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "ConnectionStrings": {"HmcLiveFeedsConnectionString": "Server=tcp:phccsqldevqc.database.windows.net,1433;Initial Catalog=hmc_livefeeds;Persist Security Info=False;User ID=esrvdevuser;Password=************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=True;Connection Timeout=30;"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "Hl7Enhancement": {"OriginalMessagesPath": "hl7-messages/original", "EnhancedMessagesPath": "hl7-messages/enhanced", "BlobContainerName": "hl7-messages", "EnableDatabaseLookup": true, "RequiredFields": ["QATAR_ID_EXP", "HC_EXP_DATE", "FAMILY_PHYSICIAN", "PRIM_ORG_NAME"]}}