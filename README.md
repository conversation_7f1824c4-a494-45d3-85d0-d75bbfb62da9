# HL7 Message Enhancement System

A C# .NET 8 console application that implements HL7 message enhancement with Azure Blob trigger simulation, Entity Framework Core database integration, and local file storage with timestamped audit trails.

## Project Overview

This application processes healthcare HL7 (Health Level 7) messages through a comprehensive enhancement workflow that:

1. **Receives HL7 messages** via Azure Blob trigger simulation or local file processing
2. **Saves original messages** to `hl7-messages/original/` with timestamped filenames
3. **Extracts patient MRN** from PID.3 segment for database lookup
4. **Queries HMC_Livefeeds.Patients table** for missing patient data using Entity Framework Core
5. **Enhances messages** by adding missing OBX segments for required fields
6. **Saves enhanced messages** to `hl7-messages/enhanced/` with timestamped filenames
7. **Provides graceful error handling** for database and parsing failures

## Technology Stack

- **.NET 8.0** - Modern LTS framework with C# 12 features
- **HL7-V2 (v3.4.0)** - Professional HL7 message parsing library
- **Entity Framework Core (v9.0.5)** - Database ORM for patient data queries
- **Azure.Storage.Blobs (v12.19.1)** - Azure Blob Storage client for message reception
- **Microsoft.Extensions.Configuration (v9.0.5)** - Configuration management
- **Microsoft.Extensions.Logging (v9.0.5)** - Structured logging framework
- **Markdig (v0.41.1)** - Markdown parsing and processing (legacy support)
- **System.Text.Json (v9.0.5)** - High-performance JSON serialization

## Architecture

The application follows modern C# patterns with dependency injection:
- **Dependency Injection** with Microsoft.Extensions.DependencyInjection
- **Configuration Management** using appsettings.json
- **Structured Logging** with Microsoft.Extensions.Logging
- **Service-Oriented Architecture** with separate concerns
- **Entity Framework Core** for database operations
- **Async/await patterns** for efficient I/O operations

## HL7 Enhancement Workflow

### Step 1: Message Reception
- **Azure Blob Trigger Simulation**: Monitors blob storage for new HL7 files
- **Local Directory Processing**: Processes HL7 files from local directories
- **Markdown File Processing**: Legacy support for markdown-exported HL7 messages

### Step 2: Original Message Storage
- **File Validation**: Validates HL7 message format and structure
- **Timestamped Storage**: Saves to `hl7-messages/original/{filename}_{timestamp}_original.hl7`
- **Audit Trail**: Maintains complete audit trail of all processed messages

### Step 3: Patient Data Extraction
- **MRN Extraction**: Extracts Medical Record Number from PID.3 segment
- **HL7 Parsing**: Uses HL7-V2 library for robust message parsing
- **Field Identification**: Identifies existing OBX segments and their values

### Step 4: Database Query
- **Patient Lookup**: Queries HMC_Livefeeds.Patients table using Health Card Number
- **Data Retrieval**: Retrieves missing patient data fields:
  - `QidExpiryDate` → QATAR_ID_EXP OBX segment
  - `HcExpiryDate` → HC_EXP_DATE OBX segment
  - `AssignedFamilyPhysicianId` → FAMILY_PHYSICIAN OBX segment
  - `AssignedHcCode` → PRIM_ORG_NAME OBX segment
- **Error Handling**: Graceful handling of database connection failures

### Step 5: Message Enhancement
- **Missing Field Detection**: Compares existing OBX segments with required fields
- **OBX Segment Generation**: Creates new OBX segments for missing data
- **Message Reconstruction**: Inserts new segments maintaining HL7 structure
- **Sequence Management**: Maintains proper OBX sequence numbering

### Step 6: Enhanced Message Storage
- **Enhanced Storage**: Saves to `hl7-messages/enhanced/{filename}_{timestamp}_enhanced.hl7`
- **Processing Results**: Logs enhancement status and processing statistics
- **Error Recovery**: Returns original message if enhancement fails

## Project Structure

```
Hl7Converter/
├── Program.cs                    # Main entry point with dependency injection
├── Hl7EnhancementConfig.cs      # Configuration model for enhancement settings
├── Hl7ProcessingResult.cs       # Processing result model
├── Hl7ExtractedFields.cs        # Extracted fields record
├── Services/                    # Service layer
│   ├── PatientDataService.cs    # Database query service
│   ├── FileStorageService.cs    # Local file storage with audit trails
│   ├── Hl7MessageEnhancer.cs    # Message enhancement logic
│   ├── Hl7EnhancementService.cs # Main orchestration service
│   └── BlobTriggerSimulator.cs  # Azure Blob trigger simulation
├── Context/                     # Entity Framework models
│   ├── HmcLiveFeedContext.cs    # Database context
│   ├── Patient.cs               # Patient entity model
│   ├── AppointmentBooking.cs    # Appointment entity model
│   └── Visit.cs                 # Visit entity model
├── hl7-messages/               # Local file storage
│   ├── original/               # Original HL7 messages with timestamps
│   └── enhanced/               # Enhanced HL7 messages with timestamps
├── MDFiles/                    # Sample and test files
│   ├── *.hl7                   # Sample HL7 message files
│   ├── *.md                    # Legacy Markdown files
│   └── *.json                  # Generated JSON output files
├── appsettings.json            # Configuration file
└── Hl7Converter.csproj         # Project file with dependencies
```

## Implementation Features

### ✅ Core Enhancement Features
- **Azure Blob Trigger Simulation**: Monitors blob storage for new HL7 files
- **Local File Processing**: Processes HL7 files from local directories
- **Database Integration**: Entity Framework Core with HMC_Livefeeds database
- **Patient Data Lookup**: Queries patients by Health Card Number (MRN)
- **Message Enhancement**: Adds missing OBX segments from database data
- **Timestamped File Storage**: Audit trails for original and enhanced messages
- **Graceful Error Handling**: Database failures, parsing errors, file I/O errors

### ✅ Required OBX Fields Enhancement
- **QATAR_ID_EXP** (DT): Qatar ID expiration date from `QidExpiryDate`
- **HC_EXP_DATE** (DT): Healthcare card expiration date from `HcExpiryDate`
- **FAMILY_PHYSICIAN** (CD): Family physician ID from `AssignedFamilyPhysicianId`
- **PRIM_ORG_NAME** (TX): Primary organization name from `AssignedHcCode`

### ✅ Technical Features
- **Dependency Injection**: Modern service architecture
- **Configuration Management**: appsettings.json configuration
- **Structured Logging**: Microsoft.Extensions.Logging
- **HL7 Message Parsing**: Uses HL7-V2 library for robust parsing
- **Legacy Support**: Backward compatibility with Markdown processing
- **Processing Statistics**: Detailed reporting and metrics

## Configuration

### appsettings.json
```json
{
  "Values": {
    "AzureWebJobsStorage": "your_storage_connection_string"
  },
  "ConnectionStrings": {
    "HmcLiveFeedsConnectionString": "your_database_connection_string"
  },
  "Hl7Enhancement": {
    "OriginalMessagesPath": "hl7-messages/original",
    "EnhancedMessagesPath": "hl7-messages/enhanced",
    "BlobContainerName": "hl7-messages",
    "EnableDatabaseLookup": true,
    "RequiredFields": ["QATAR_ID_EXP", "HC_EXP_DATE", "FAMILY_PHYSICIAN", "PRIM_ORG_NAME"]
  }
}
```

## Sample HL7 Enhancement

### Before Enhancement (missing QATAR_ID_EXP):
```hl7
MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|**************||ADT^A31|Q123|P|2.3
PID|1||**********^^^MRN^MRN^RHAPSODY_CON_SYS||DOE^JOHN^MIDDLE||********|M|||123 MAIN ST^^DOHA^QA^12345||+***********|||||||||||||||||||||
OBX|1|CE|COUNTRY_RES||Qatar||||||
OBX|2|DT|HC EXP DATE||********||||||
```

### After Enhancement (QATAR_ID_EXP added):
```hl7
MSH|^~\&|Millennium|HMC|RHAPSODY_ADT|HMC|**************||ADT^A31|Q123|P|2.3
PID|1||**********^^^MRN^MRN^RHAPSODY_CON_SYS||DOE^JOHN^MIDDLE||********|M|||123 MAIN ST^^DOHA^QA^12345||+***********|||||||||||||||||||||
OBX|1|CE|COUNTRY_RES||Qatar||||||
OBX|2|DT|HC EXP DATE||********||||||
OBX|3|DT|QATAR_ID_EXP||********||||||
```

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- Visual Studio 2022 or JetBrains Rider (recommended)
- SQL Server access for HMC_Livefeeds database
- Azure Storage Account (optional, for blob processing)

### Installation
1. Clone the repository
2. Restore NuGet packages:
   ```bash
   dotnet restore
   ```
3. Update `appsettings.json` with your connection strings
4. Ensure database connectivity to HMC_Livefeeds

### Running the Application
1. Build and run:
   ```bash
   dotnet run
   ```
2. Select processing mode:
   - **Option 1**: Process from Azure Blob Storage
   - **Option 2**: Process from local directory (recommended for testing)
   - **Option 3**: Process from Markdown file (legacy mode)

### Sample Files
The `MDFiles` directory contains sample HL7 files for testing:
- `sample_hl7_message.hl7` - Basic message missing some OBX segments
- `sample_hl7_complete.hl7` - Complete message with all OBX segments

## Usage Examples

### Console Application Output
```
=== HL7 Message Enhancement System ===
Starting application...

Select processing mode:
1. Process from Azure Blob Storage
2. Process from local directory
3. Process from Markdown file (legacy mode)
Enter your choice (1-3): 2

Enter the local directory path containing HL7 files: MDFiles
Using default directory: MDFiles

Processing HL7 messages from directory: MDFiles...

Processing file: sample_hl7_message.hl7
✓ Enhanced: sample_hl7_message.hl7 (MRN: **********)

Processing file: sample_hl7_complete.hl7
✓ Processed: sample_hl7_complete.hl7 (MRN: **********) - No enhancement needed

=== Processing Summary ===
Total processed: 2
Enhanced: 1
Failed: 0
```

### File Storage Results
After processing, you'll find:
- **Original messages**: `hl7-messages/original/sample_hl7_message_20250118_143022_original.hl7`
- **Enhanced messages**: `hl7-messages/enhanced/sample_hl7_message_20250118_143022_enhanced.hl7`

## Error Handling

The application includes comprehensive error handling:
- **Database Connection Failures**: Logs error, returns original message unchanged
- **Patient Not Found**: Logs warning, returns original message unchanged
- **HL7 Parsing Failures**: Logs error, returns error status
- **File I/O Errors**: Logs error but continues processing other files
- **Missing Configuration**: Validates configuration on startup

## Testing

### Unit Testing
To test the implementation:
1. Use the provided sample HL7 files in `MDFiles/`
2. Run with local directory processing mode
3. Verify original and enhanced files are created with timestamps
4. Check console output for processing statistics

### Database Testing
- Ensure the Health Card Number in sample files exists in your database
- Test with both existing and non-existing patient records
- Verify graceful handling when database is unavailable

## Contributing

This project follows modern C# development practices:
- **Dependency Injection**: Service-oriented architecture
- **Entity Framework Core**: Database operations
- **Structured Logging**: Comprehensive logging and diagnostics
- **Configuration Management**: Externalized configuration
- **Error Handling**: Graceful degradation and recovery
- **Async/Await Patterns**: Efficient I/O operations

## License

This project is for educational and healthcare data processing purposes.
