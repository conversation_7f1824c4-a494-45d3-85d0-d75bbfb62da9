using Hl7Converter.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace Hl7Converter.Services;

/// <summary>
/// Service for querying patient data from HMC_Livefeeds database
/// </summary>
public class PatientDataService(HmcLiveFeedContext context, ILogger<PatientDataService> logger)
{
    /// <summary>
    /// Retrieves patient data by Health Card Number (MRN)
    /// </summary>
    /// <returns>Patient data or null if not found</returns>
    private readonly HashSet<string> _foundHealthCardNumbers = new();
    private int _foundPatientCount;

    public async Task<Patient?> GetPatientByHealthCardNumberAsync(string healthCardNumber)
    {
        try
        {
            logger.LogInformation("Querying patient data for Health Card Number: {HealthCardNumber}", healthCardNumber);

            var patient = await context.Patients
                .FirstOrDefaultAsync(p => p.HcNumber == healthCardNumber);

            if (patient != null)
            {
                logger.LogInformation("Patient found for Health Card Number: {HealthCardNumber}", healthCardNumber);
                // Accumulate health card number and count
                if (_foundHealthCardNumbers.Add(healthCardNumber))
                    _foundPatientCount++;
                await SaveHealthCardNumbersJsonAsync();
            }
            else
            {
                logger.LogWarning("Patient not found for Health Card Number: {HealthCardNumber}", healthCardNumber);
            }

            return patient;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Database error while querying patient data for Health Card Number: {HealthCardNumber}", healthCardNumber);
            return null;
        }
    }

    // Save health card numbers and count to JSON in project root
    private async Task SaveHealthCardNumbersJsonAsync()
    {
        var result = new
        {
            totalCount = _foundPatientCount,
            healthCardNumbers = _foundHealthCardNumbers.ToList()
        };
        var json = JsonSerializer.Serialize(result, new JsonSerializerOptions { WriteIndented = true });
        var projectRoot = GetProjectRootPath();
        var outputPath = Path.Combine(projectRoot, "queried_health_cards.json");
        await File.WriteAllTextAsync(outputPath, json);
    }

    /// <summary>
    /// Checks if a database connection is available
    /// </summary>
    /// <returns>True if a database is accessible, false otherwise</returns>
    public async Task<bool> IsDatabaseAvailableAsync()
    {
        try
        {
            await context.Database.CanConnectAsync();
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Database connection failed");
            return false;
        }
    }

    private static string GetProjectRootPath()
    {
        var currentDir = AppContext.BaseDirectory;
        var dirInfo = new DirectoryInfo(currentDir);
        while (dirInfo != null && !File.Exists(Path.Combine(dirInfo.FullName, "Program.cs")))
        {
            dirInfo = dirInfo.Parent;
        }
        return dirInfo?.FullName ?? currentDir;
    }
}
