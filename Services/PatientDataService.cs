using Hl7Converter.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Hl7Converter.Services;

/// <summary>
/// Service for querying patient data from HMC_Livefeeds database
/// </summary>
public class PatientDataService(HmcLiveFeedContext context, ILogger<PatientDataService> logger)
{
    /// <summary>
    /// Retrieves patient data by Health Card Number (MRN)
    /// </summary>
    /// <param name="healthCardNumber">Health Card Number from PID.3 segment</param>
    /// <returns>Patient data or null if not found</returns>
    public async Task<Patient?> GetPatientByHealthCardNumberAsync(string healthCardNumber)
    {
        try
        {
            logger.LogInformation("Querying patient data for Health Card Number: {HealthCardNumber}", healthCardNumber);

            var patient = await context.Patients
                .FirstOrDefaultAsync(p => p.HcNumber == healthCardNumber);

            if (patient != null)
            {
                logger.LogInformation("Patient found for Health Card Number: {HealthCardNumber}", healthCardNumber);
            }
            else
            {
                logger.LogWarning("Patient not found for Health Card Number: {HealthCardNumber}", healthCardNumber);
            }

            return patient;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Database error while querying patient data for Health Card Number: {HealthCardNumber}", healthCardNumber);
            return null;
        }
    }

    /// <summary>
    /// Checks if database connection is available
    /// </summary>
    /// <returns>True if a database is accessible, false otherwise</returns>
    public async Task<bool> IsDatabaseAvailableAsync()
    {
        try
        {
            await context.Database.CanConnectAsync();
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Database connection failed");
            return false;
        }
    }
}
