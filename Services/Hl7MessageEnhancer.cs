using Hl7Converter.Context;
using Microsoft.Extensions.Logging;
using NHapi.Base.Model;
using NHapi.Base.Parser;
using NHapi.Base.Util;

namespace Hl7Converter.Services;

/// <summary>
/// Service for enhancing HL7 messages with missing OBX segments using the nHapi library.
/// It parses HL7 messages, identifies missing required OBX segments based on patient data,
/// adds them, and then re-encodes the message.
/// </summary>
public class Hl7MessageEnhancer(ILogger<Hl7MessageEnhancer> logger)
{
    private readonly PipeParser _pipeParser = new(); // nHapi's standard parser for ER7 (pipe-and-hat) encoded messages.

    private const string DateFormat = "yyyyMMdd"; // Standard HL7 date format.
    private const string DefaultObservationResultStatus = "F"; // Default status for new OBX segments (F = Final).

    /// <summary>
    /// Enhances an HL7 message string by adding missing OBX segments.
    /// This method uses nHapi for robust parsing and manipulation of the HL7 structure.
    /// </summary>
    /// <param name="originalHl7">The original HL7 message content as a string.</param>
    /// <param name="patient">The Patient object containing data to populate new OBX segments.</param>
    /// <param name="extractedFields">An object indicating which of the target OBX fields might already exist in the message.</param>
    /// <returns>The enhanced HL7 message as a string. Returns the original message if parsing fails or if an error occurs during enhancement.</returns>
    public string EnhanceMessage(string originalHl7, Patient patient, Hl7ExtractedFields extractedFields)
    {
        logger.LogInformation("Starting HL7 message enhancement using nHapi Terser");
        IMessage message;

        try
        {
            message = _pipeParser.Parse(originalHl7);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to parse original HL7 message with nHapi. Returning original message");
            return originalHl7;
        }

        try
        {
            var terser = new Terser(message);
            int obxCount = 0;
            int maxObxToCheck = 50; // Safety limit to prevent infinite loops
            
            // Count existing OBX segments (OBX is usually in the ORDER_OBSERVATION group, but we search globally)
            while (obxCount < maxObxToCheck)
            {
                try
                {
                    var obxPath = $"/OBX({obxCount})"; // Using an absolute path with a leading slash
                    var segment = terser.GetSegment(obxPath);
                    if (segment == null)
                        break;
                    obxCount++;
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, "Error while checking OBX segment at index {Index}. Stopping OBX count", obxCount);
                    break;
                }
            }
            
            logger.LogInformation("Found {Count} existing OBX segments", obxCount);
            int nextObxSequence = obxCount + 1;
            int addedSegments = 0;

            // Helper to add OBX segment using Terser
            void AddObx(string valueType, string identifier, string value)
            {
                try
                {
                    // Create a new OBX segment
                    var obxPath = $"/OBX({obxCount + addedSegments})";
                    ISegment newObx = terser.GetSegment("/OBX(0)");
                    
                    if (newObx == null)
                    {
                        // Try to add after a PID segment
                        var structure = message.GetStructureName();
                        logger.LogInformation("Message structure: {Structure}", structure);
                        
                        // Fall back to string manipulation if Terser fails
                        logger.LogInformation("Falling back to string manipulation for adding OBX segment");
                        return;
                    }
                    
                    terser.Set($"{obxPath}-1", (nextObxSequence + addedSegments).ToString()); // Set ID (OBX-1)
                    terser.Set($"{obxPath}-2", valueType); // Value Type (OBX-2)
                    terser.Set($"{obxPath}-3-1", identifier); // Observation Identifier (OBX-3.1)
                    terser.Set($"{obxPath}-5", value); // Observation Value (OBX-5)
                    terser.Set($"{obxPath}-11", DefaultObservationResultStatus); // Observation Result Status (OBX-11)
                    logger.LogInformation("Added {Identifier} OBX segment: {Value}", identifier, value);
                    addedSegments++;
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to add OBX segment for {Identifier}. Continuing with next field", identifier);
                }
            }

            // Try to add each segment but continue even if one fails
            try
            {
                if (string.IsNullOrEmpty(extractedFields.QATAR_ID_EXP) && patient.QidExpiryDate.HasValue)
                {
                    AddObx("DT", "QATAR_ID_EXP", patient.QidExpiryDate.Value.ToString(DateFormat));
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error adding QATAR_ID_EXP segment");
            }

            try
            {
                if (string.IsNullOrEmpty(extractedFields.HC_EXP_DATE) && patient.HcExpiryDate.HasValue)
                {
                    AddObx("DT", "HC EXP DATE", patient.HcExpiryDate.Value.ToString(DateFormat));
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error adding HC_EXP_DATE segment");
            }

            try
            {
                if (string.IsNullOrEmpty(extractedFields.FAMILY_PHYSICIAN) &&
                    !string.IsNullOrEmpty(patient.AssignedFamilyPhysicianId))
                {
                    AddObx("ST", "FAMILY_PHYSICIAN", patient.AssignedFamilyPhysicianId);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error adding FAMILY_PHYSICIAN segment");
            }

            try
            {
                if (string.IsNullOrEmpty(extractedFields.PRIM_ORG_NAME) && !string.IsNullOrEmpty(patient.AssignedHcCode))
                {
                    AddObx("TX", "PRIM_ORG_NAME", patient.AssignedHcCode);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error adding PRIM_ORG_NAME segment");
            }

            if (addedSegments > 0)
            {
                logger.LogInformation("HL7 message enhancement completed. Added {Count} OBX segments", addedSegments);
                try
                {
                    return _pipeParser.Encode(message);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to encode enhanced message. Returning original");
                    return originalHl7;
                }
            }

            logger.LogInformation(
                "No new OBX segments were added to the HL7 message as conditions were not met or data was already present");
            return originalHl7;
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "Error during the enhancement phase of HL7 message processing with nHapi Terser. Returning original message");
            return originalHl7;
        }
    }
}