using Hl7Converter.Context;
using Microsoft.Extensions.Logging;
using NHapi.Base.Model;
using NHapi.Base.Parser;
using NHapi.Base.Util;
using NHapi.Model.V25.Message;
using NHapi.Model.V25.Segment;
using NHapi.Model.V25.Group;
using NHapi.Model.V25.Datatype;

namespace Hl7Converter.Services;

/// <summary>
/// Service for enhancing HL7 messages with missing OBX segments using the nHapi library.
/// It parses HL7 messages, identifies missing required OBX segments based on patient data,
/// adds them, and then re-encodes the message.
/// </summary>
public class Hl7MessageEnhancer(ILogger<Hl7MessageEnhancer> logger)
{
    private readonly PipeParser _pipeParser = new(); // nHapi's standard parser for ER7 (pipe-and-hat) encoded messages.

    private const string DateFormat = "yyyyMMdd"; // Standard HL7 date format.
    private const string DefaultObservationResultStatus = "F"; // Default status for new OBX segments (F = Final).

    /// <summary>
    /// Enhances an HL7 message string by adding missing OBX segments.
    /// This method uses nHapi's strongly-typed message structures for robust parsing and manipulation.
    /// </summary>
    /// <param name="originalHl7">The original HL7 message content as a string.</param>
    /// <param name="patient">The Patient object containing data to populate new OBX segments.</param>
    /// <param name="extractedFields">An object indicating which of the target OBX fields might already exist in the message.</param>
    /// <returns>The enhanced HL7 message as a string. Returns the original message if parsing fails or if an error occurs during enhancement.</returns>
    public string EnhanceMessage(string originalHl7, Patient patient, Hl7ExtractedFields extractedFields)
    {
        logger.LogInformation("Starting HL7 message enhancement using nHapi strongly-typed structures");
        IMessage message;

        try
        {
            message = _pipeParser.Parse(originalHl7);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to parse original HL7 message with nHapi. Returning original message");
            return originalHl7;
        }

        try
        {
            var messageType = GetMessageType(message);
            logger.LogInformation("Detected message type: {MessageType}", messageType);

            int addedSegments = 0;

            // Use strongly-typed message structure based on message type
            switch (messageType)
            {
                case "ADT_A01":
                case "ADT_A04":
                case "ADT_A08":
                    addedSegments = EnhanceAdtMessage(message, patient, extractedFields);
                    break;
                case "ORU_R01":
                    addedSegments = EnhanceOruMessage(message, patient, extractedFields);
                    break;
                default:
                    logger.LogWarning("Unsupported message type: {MessageType}. Falling back to generic approach", messageType);
                    addedSegments = EnhanceGenericMessage(message, patient, extractedFields);
                    break;
            }

            if (addedSegments > 0)
            {
                logger.LogInformation("HL7 message enhancement completed. Added {Count} OBX segments", addedSegments);
                try
                {
                    return _pipeParser.Encode(message);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex, "Failed to encode enhanced message. Returning original");
                    return originalHl7;
                }
            }

            logger.LogInformation(
                "No new OBX segments were added to the HL7 message as conditions were not met or data was already present");
            return originalHl7;
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "Error during the enhancement phase of HL7 message processing with nHapi strongly-typed structures. Returning original message");
            return originalHl7;
        }
    }

    /// <summary>
    /// Gets the message type from the MSH segment
    /// </summary>
    private string GetMessageType(IMessage message)
    {
        try
        {
            var terser = new Terser(message);
            var messageType = terser.Get("/MSH-9-1");
            var triggerEvent = terser.Get("/MSH-9-2");
            return $"{messageType}_{triggerEvent}";
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error getting message type from MSH segment");
            return "UNKNOWN";
        }
    }

    /// <summary>
    /// Enhances ADT messages (A01, A04, A08) using strongly-typed structures
    /// </summary>
    private int EnhanceAdtMessage(IMessage message, Patient patient, Hl7ExtractedFields extractedFields)
    {
        try
        {
            // Cast to ADT_A01 (works for A01, A04, A08 as they have similar structure)
            if (message is not ADT_A01 adtMessage)
            {
                logger.LogWarning("Message is not an ADT_A01 type. Falling back to generic approach");
                return EnhanceGenericMessage(message, patient, extractedFields);
            }

            int addedSegments = 0;

            // Get the patient group which contains OBX segments
            var patientGroup = adtMessage.GetPATIENT();
            if (patientGroup == null)
            {
                logger.LogWarning("No PATIENT group found in ADT message");
                return 0;
            }

            // Count existing OBX segments
            int existingObxCount = patientGroup.OBXRepetitionsUsed;
            logger.LogInformation("Found {Count} existing OBX segments in ADT message", existingObxCount);

            // Add missing OBX segments
            addedSegments += TryAddObxSegment(patientGroup, extractedFields.QATAR_ID_EXP, patient.QidExpiryDate,
                "DT", "QATAR_ID_EXP", existingObxCount + addedSegments + 1);

            addedSegments += TryAddObxSegment(patientGroup, extractedFields.HC_EXP_DATE, patient.HcExpiryDate,
                "DT", "HC EXP DATE", existingObxCount + addedSegments + 1);

            addedSegments += TryAddObxSegment(patientGroup, extractedFields.FAMILY_PHYSICIAN, patient.AssignedFamilyPhysicianId,
                "ST", "FAMILY_PHYSICIAN", existingObxCount + addedSegments + 1);

            addedSegments += TryAddObxSegment(patientGroup, extractedFields.PRIM_ORG_NAME, patient.AssignedHcCode,
                "TX", "PRIM_ORG_NAME", existingObxCount + addedSegments + 1);

            return addedSegments;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error enhancing ADT message with strongly-typed structures");
            return 0;
        }
    }

    /// <summary>
    /// Enhances ORU messages using strongly-typed structures
    /// </summary>
    private int EnhanceOruMessage(IMessage message, Patient patient, Hl7ExtractedFields extractedFields)
    {
        try
        {
            if (message is not ORU_R01 oruMessage)
            {
                logger.LogWarning("Message is not an ORU_R01 type. Falling back to generic approach");
                return EnhanceGenericMessage(message, patient, extractedFields);
            }

            int addedSegments = 0;

            // Get the patient result group
            var patientResult = oruMessage.GetPATIENT_RESULT();
            if (patientResult == null)
            {
                logger.LogWarning("No PATIENT_RESULT group found in ORU message");
                return 0;
            }

            // Get or create an ORDER_OBSERVATION group
            var orderObservation = patientResult.GetORDER_OBSERVATION(0);
            if (orderObservation == null)
            {
                logger.LogWarning("No ORDER_OBSERVATION group found in ORU message");
                return 0;
            }

            // Count existing OBX segments
            int existingObxCount = orderObservation.OBSERVATIONRepetitionsUsed;
            logger.LogInformation("Found {Count} existing OBX segments in ORU message", existingObxCount);

            // Add missing OBX segments to OBSERVATION groups
            addedSegments += TryAddObxToOruObservation(orderObservation, extractedFields.QATAR_ID_EXP, patient.QidExpiryDate,
                "DT", "QATAR_ID_EXP", existingObxCount + addedSegments + 1);

            addedSegments += TryAddObxToOruObservation(orderObservation, extractedFields.HC_EXP_DATE, patient.HcExpiryDate,
                "DT", "HC EXP DATE", existingObxCount + addedSegments + 1);

            addedSegments += TryAddObxToOruObservation(orderObservation, extractedFields.FAMILY_PHYSICIAN, patient.AssignedFamilyPhysicianId,
                "ST", "FAMILY_PHYSICIAN", existingObxCount + addedSegments + 1);

            addedSegments += TryAddObxToOruObservation(orderObservation, extractedFields.PRIM_ORG_NAME, patient.AssignedHcCode,
                "TX", "PRIM_ORG_NAME", existingObxCount + addedSegments + 1);

            return addedSegments;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error enhancing ORU message with strongly-typed structures");
            return 0;
        }
    }

    /// <summary>
    /// Generic enhancement method using Terser (fallback for unsupported message types)
    /// </summary>
    private int EnhanceGenericMessage(IMessage message, Patient patient, Hl7ExtractedFields extractedFields)
    {
        try
        {
            var terser = new Terser(message);
            int addedSegments = 0;

            // Count existing OBX segments
            int obxCount = 0;
            while (obxCount < 50) // Safety limit
            {
                try
                {
                    var segment = terser.GetSegment($"/OBX({obxCount})");
                    if (segment == null) break;
                    obxCount++;
                }
                catch
                {
                    break;
                }
            }

            logger.LogInformation("Found {Count} existing OBX segments using generic approach", obxCount);

            // Try to add segments using generic terser approach
            addedSegments += TryAddGenericObx(terser, extractedFields.QATAR_ID_EXP, patient.QidExpiryDate?.ToString(DateFormat),
                "DT", "QATAR_ID_EXP", obxCount + addedSegments);

            addedSegments += TryAddGenericObx(terser, extractedFields.HC_EXP_DATE, patient.HcExpiryDate?.ToString(DateFormat),
                "DT", "HC EXP DATE", obxCount + addedSegments);

            addedSegments += TryAddGenericObx(terser, extractedFields.FAMILY_PHYSICIAN, patient.AssignedFamilyPhysicianId,
                "ST", "FAMILY_PHYSICIAN", obxCount + addedSegments);

            addedSegments += TryAddGenericObx(terser, extractedFields.PRIM_ORG_NAME, patient.AssignedHcCode,
                "TX", "PRIM_ORG_NAME", obxCount + addedSegments);

            return addedSegments;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error enhancing message with generic approach");
            return 0;
        }
    }

    /// <summary>
    /// Helper method to add OBX segment to ADT patient group
    /// </summary>
    private int TryAddObxSegment(ADT_A01_PATIENT patientGroup, string? existingValue, DateTime? dateValue,
        string valueType, string identifier, int sequenceNumber)
    {
        if (!string.IsNullOrEmpty(existingValue)) return 0;

        string? value = dateValue?.ToString(DateFormat);
        return TryAddObxSegment(patientGroup, existingValue, value, valueType, identifier, sequenceNumber);
    }

    /// <summary>
    /// Helper method to add OBX segment to ADT patient group
    /// </summary>
    private int TryAddObxSegment(ADT_A01_PATIENT patientGroup, string? existingValue, string? value,
        string valueType, string identifier, int sequenceNumber)
    {
        if (!string.IsNullOrEmpty(existingValue) || string.IsNullOrEmpty(value)) return 0;

        try
        {
            var newObx = patientGroup.GetOBX(patientGroup.OBXRepetitionsUsed);
            PopulateObxSegment(newObx, sequenceNumber, valueType, identifier, value);
            logger.LogInformation("Added {Identifier} OBX segment to ADT message: {Value}", identifier, value);
            return 1;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to add OBX segment for {Identifier} to ADT message", identifier);
            return 0;
        }
    }

    /// <summary>
    /// Helper method to add OBX segment to ORU observation group
    /// </summary>
    private int TryAddObxToOruObservation(ORU_R01_ORDER_OBSERVATION orderObservation, string? existingValue, DateTime? dateValue,
        string valueType, string identifier, int sequenceNumber)
    {
        if (!string.IsNullOrEmpty(existingValue)) return 0;

        string? value = dateValue?.ToString(DateFormat);
        return TryAddObxToOruObservation(orderObservation, existingValue, value, valueType, identifier, sequenceNumber);
    }

    /// <summary>
    /// Helper method to add OBX segment to ORU observation group
    /// </summary>
    private int TryAddObxToOruObservation(ORU_R01_ORDER_OBSERVATION orderObservation, string? existingValue, string? value,
        string valueType, string identifier, int sequenceNumber)
    {
        if (!string.IsNullOrEmpty(existingValue) || string.IsNullOrEmpty(value)) return 0;

        try
        {
            var observation = orderObservation.GetOBSERVATION(orderObservation.OBSERVATIONRepetitionsUsed);
            var newObx = observation.OBX;
            PopulateObxSegment(newObx, sequenceNumber, valueType, identifier, value);
            logger.LogInformation("Added {Identifier} OBX segment to ORU message: {Value}", identifier, value);
            return 1;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to add OBX segment for {Identifier} to ORU message", identifier);
            return 0;
        }
    }

    /// <summary>
    /// Helper method to add OBX segment using generic Terser approach
    /// </summary>
    private int TryAddGenericObx(Terser terser, string? existingValue, string? value,
        string valueType, string identifier, int obxIndex)
    {
        if (!string.IsNullOrEmpty(existingValue) || string.IsNullOrEmpty(value)) return 0;

        try
        {
            var obxPath = $"/OBX({obxIndex})";

            // Try to set the fields directly
            terser.Set($"{obxPath}-1", (obxIndex + 1).ToString());
            terser.Set($"{obxPath}-2", valueType);
            terser.Set($"{obxPath}-3-1", identifier);
            terser.Set($"{obxPath}-5", value);
            terser.Set($"{obxPath}-11", DefaultObservationResultStatus);

            logger.LogInformation("Added {Identifier} OBX segment using generic approach: {Value}", identifier, value);
            return 1;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to add OBX segment for {Identifier} using generic approach", identifier);
            return 0;
        }
    }

    /// <summary>
    /// Populates an OBX segment with the provided data
    /// </summary>
    private void PopulateObxSegment(OBX obx, int sequenceNumber, string valueType, string identifier, string value)
    {
        obx.SetIDOBX.Value = sequenceNumber.ToString();
        obx.ValueType.Value = valueType;
        obx.ObservationIdentifier.Identifier.Value = identifier;
        obx.GetObservationValue(0).Data = new ST(obx.Message) { Value = value };
        obx.ObservationResultStatus.Value = DefaultObservationResultStatus;
    }
}