using Efferent.HL7.V2;
using Microsoft.Extensions.Logging;

namespace Hl7Converter.Services;

public class Hl7EnhancementService(
    PatientDataService patientDataService,
    FileStorageService fileStorageService,
    Hl7MessageEnhancer messageEnhancer,
    ILogger<Hl7EnhancementService> logger,
    Hl7EnhancementConfig config)
{
    public async Task<Hl7ProcessingResult> ProcessMessageAsync(string filename, string hl7Content)
    {
        var result = new Hl7ProcessingResult
        {
            Filename = filename,
            ProcessingStartTime = DateTime.UtcNow
        };

        try
        {
            // Step 1: Parse HL7 message
            var message = new Message(hl7Content);
            var isParsed = message.ParseMessage();

            if (!isParsed)
            {
                result.Status = "Failed";
                result.ErrorMessage = "Failed to parse HL7 message";
                logger.LogError("Failed to parse HL7 message for file: {Filename}", filename);
                return result;
            }

            // Step 2: Extract MRN from PID.3 segment
            var mrn = ExtractMrnFromMessage(message);
            if (string.IsNullOrEmpty(mrn))
            {
                result.Status = "Failed";
                result.ErrorMessage = "Could not extract MRN from PID.3 segment";
                logger.LogError("Could not extract MRN from PID.3 segment for file: {Filename}", filename);
                return result;
            }

            result.ExtractedMrn = mrn;
            logger.LogInformation("Extracted MRN: {Mrn} from file: {Filename}", mrn, filename);

            // Step 3: Extract current fields from a message
            Hl7ExtractedFields extractedFields = ExtractFieldsFromMessage(message);
            result.ExtractedFields = extractedFields;

            // Step 4: Query database for patient data (if enabled)
            string enhancedContent = hl7Content; // Default to original content

            if (config.EnableDatabaseLookup)
            {
                var patient = await patientDataService.GetPatientByHealthCardNumberAsync(mrn);
                
                if (patient is not null)
                {
                    logger.LogInformation("Patient data found for MRN: {Mrn}", mrn);
                    
                    // Step 5: Enhance a message with missing fields
                    enhancedContent = messageEnhancer.EnhanceMessage(hl7Content, patient, extractedFields);
                    result.WasEnhanced = !string.Equals(hl7Content, enhancedContent, StringComparison.Ordinal);
                }
                else
                {
                    logger.LogWarning("Patient not found for MRN: {Mrn}. Returning original message", mrn);
                }
            }
            else
            {
                logger.LogInformation("Database lookup disabled. Returning original message");
            }

            // Step 6: Save messages only if they were enhanced
            if (result.WasEnhanced)
            {
                // Save the original message for reference
                result.OriginalFilePath = await fileStorageService.SaveOriginalMessageAsync(filename, hl7Content);
                // Save an enhanced message
                result.EnhancedFilePath = await fileStorageService.SaveEnhancedMessageAsync(filename, enhancedContent);
                logger.LogInformation("Saved original and enhanced messages because enhancements were made");
            }
            else
            {
                logger.LogInformation("No enhancements were made to the message. Skipping file storage");
            }

            result.Status = "Success";
            result.ProcessingEndTime = DateTime.UtcNow;
            result.ProcessingDuration = result.ProcessingEndTime.Value - result.ProcessingStartTime;

            logger.LogInformation("HL7 message processing completed successfully for file: {Filename}. Enhanced: {WasEnhanced}", 
                filename, result.WasEnhanced);

            return result;
        }
        catch (Exception ex)
        {
            result.Status = "Failed";
            result.ErrorMessage = ex.Message;
            result.ProcessingEndTime = DateTime.UtcNow;
            result.ProcessingDuration = result.ProcessingEndTime.Value - result.ProcessingStartTime;

            logger.LogError(ex, "Error processing HL7 message for file: {Filename}", filename);
            return result;
        }
    }
    
    private string? ExtractMrnFromMessage(Message message)
    {
        try
        {
            // PID.3 contains patient identifiers, look for MRN
            var patientId = message.GetValue("PID.3");

            if (string.IsNullOrEmpty(patientId)) return null;
            // Handle multiple identifiers separated by ~
            var identifiers = patientId.Split('~');
                
            foreach (var identifier in identifiers)
            {
                var parts = identifier.Split('^');
                if (parts.Length >= 5 && parts[4].Equals("MRN", StringComparison.OrdinalIgnoreCase))
                {
                    return parts[0]; // Return the ID value
                }
            }
                
            // If no MRN type found, return the first identifier
            var firstIdParts = identifiers[0].Split('^');
            return firstIdParts[0];

        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error extracting MRN from message");
            return null;
        }
    }
    
    private static Hl7ExtractedFields ExtractFieldsFromMessage(Message message)
    {
        var result = new Hl7ExtractedFields();
        var obxSegments = message.Segments("OBX");
        
        for (var i = 0; i < obxSegments.Count; i++)
        {
            var identifier = (message.GetValue($"OBX({i+1}).3") ?? "").Replace(" ", "").Replace("_", "").ToUpperInvariant();
            var value = message.GetValue($"OBX({i+1}).5");
            
            switch (identifier)
            {
                case "QATARIDEXP":
                    result.QATAR_ID_EXP = value;
                    break;
                case "HCEXPDATE":
                    result.HC_EXP_DATE = value;
                    break;
                case "FAMILYPHYSICIAN":
                    result.FAMILY_PHYSICIAN = value;
                    break;
                case "PRIMORGNAME":
                    result.PRIM_ORG_NAME = value;
                    break;
            }
        }
        
        return result;
    }
}
