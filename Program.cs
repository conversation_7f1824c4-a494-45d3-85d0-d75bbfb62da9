global using static System.Console;

using Azure.Storage.Blobs;
using Hl7Converter;
using Hl7Converter.Context;
using Hl7Converter.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

// Implements Azure Blob trigger simulation with database enhancement

WriteLine("=== HL7 Message Enhancement System ===");

// Build configuration
var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .Build();

// Setup dependency injection
var services = new ServiceCollection();
ServiceConfiguration.ConfigureServices(services, configuration);
var serviceProvider = services.BuildServiceProvider();

// Get logger
var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
logger.LogInformation("HL7 Enhancement System started");

try
{
    // Get main service
    var enhancementService = serviceProvider.GetRequiredService<Hl7EnhancementService>();
    var blobSimulator = serviceProvider.GetRequiredService<BlobTriggerSimulator>();

    // Check database connectivity
    var patientService = serviceProvider.GetRequiredService<PatientDataService>();
    var isDatabaseAvailable = await patientService.IsDatabaseAvailableAsync();

    if (isDatabaseAvailable)
    {
        logger.LogInformation("Database connection successful");
    }
    else
    {
        logger.LogWarning("Database connection failed - will process messages without enhancement");
    }

    // Process HL7 messages from Azure Blob Storage
    WriteLine("Processing HL7 messages from Azure Blob Storage...");
    await ProcessFromBlobStorageAsync(enhancementService, blobSimulator, logger);
}
catch (Exception ex)
{
    logger.LogError(ex, "Fatal error in application");
    WriteLine($"Fatal error: {ex.Message}");
}
finally
{
    await serviceProvider.DisposeAsync();
    WriteLine("\nPress any key to exit...");
    ReadKey();
}

// --- Configuration and Service Setup ---
static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
{
    // Add logging
    services.AddLogging(builder =>
    {
        builder.AddConsole();
        builder.AddConfiguration(configuration.GetSection("Logging"));
    });

    // Add configuration
    services.AddSingleton(configuration);

    // Bind HL7 enhancement configuration
    var hl7Config = new Hl7EnhancementConfig();
    configuration.GetSection("Hl7Enhancement").Bind(hl7Config);
    services.AddSingleton(hl7Config);

    // Add Entity Framework
    services.AddDbContext<HmcLiveFeedContext>(options =>
        options.UseSqlServer(configuration.GetConnectionString("HmcLiveFeedsConnectionString")));

    // Add Azure Blob Storage
    var storageConnectionString = configuration.GetValue<string>("Values:AzureWebJobsStorage");
    if (!string.IsNullOrEmpty(storageConnectionString))
    {
        services.AddSingleton(new BlobServiceClient(storageConnectionString));
    }

    // Add application services
    services.AddScoped<PatientDataService>();
    services.AddScoped<Hl7MessageEnhancer>();
    services.AddScoped<Hl7EnhancementService>();

    // Add a file storage service
    services.AddScoped<FileStorageService>(provider =>
    {
        var logger = provider.GetRequiredService<ILogger<FileStorageService>>();
        var config = provider.GetRequiredService<Hl7EnhancementConfig>();
        return new FileStorageService(logger, config.OriginalMessagesPath, config.EnhancedMessagesPath);
    });

    // Add blob trigger simulator
    services.AddScoped<BlobTriggerSimulator>(provider =>
    {
        var blobServiceClient = provider.GetService<BlobServiceClient>();
        var logger = provider.GetRequiredService<ILogger<BlobTriggerSimulator>>();
        var config = provider.GetRequiredService<Hl7EnhancementConfig>();

        if (blobServiceClient is not null)
        {
            return new BlobTriggerSimulator(blobServiceClient, logger, config.BlobContainerName);
        }

        // Return a mock implementation if no blob service client
        return new BlobTriggerSimulator(null!, logger, config.BlobContainerName);
    });
}

// --- Processing Methods ---
static async Task ProcessFromBlobStorageAsync(Hl7EnhancementService enhancementService, BlobTriggerSimulator blobSimulator, ILogger logger)
{
    logger.LogInformation("Starting Azure Blob Storage processing");
    WriteLine("\nProcessing HL7 messages from Azure Blob Storage...");

    var processedCount = 0;
    var enhancedCount = 0;
    var failedCount = 0;

    try
    {
        await foreach (var (blobName, content) in blobSimulator.SimulateBlobTriggerAsync())
        {
            WriteLine($"\nProcessing blob: {blobName}");

            var result = await enhancementService.ProcessMessageAsync(blobName, content);

            processedCount++;

            if (result.Status == "Success")
            {
                if (result.WasEnhanced)
                {
                    enhancedCount++;
                    WriteLine($"✓ Enhanced: {blobName} (MRN: {result.ExtractedMrn})");
                }
                else
                {
                    WriteLine($"✓ Processed: {blobName} (MRN: {result.ExtractedMrn}) - No enhancement needed");
                }
            }
            else
            {
                failedCount++;
                WriteLine($"✗ Failed: {blobName} - {result.ErrorMessage}");
            }
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error processing from blob storage");
        WriteLine($"Error: {ex.Message}");
    }

    WriteLine("\n=== Processing Summary ===");
    WriteLine($"Total processed: {processedCount}");
    WriteLine($"Enhanced: {enhancedCount}");
    WriteLine($"Failed: {failedCount}");
}