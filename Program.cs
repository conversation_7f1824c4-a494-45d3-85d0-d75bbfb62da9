using System.Text.Json;
using Azure.Storage.Blobs;
using Efferent.HL7.V2;
using Hl7Converter;
using Hl7Converter.Context;
using Hl7Converter.Services;
using Markdig;
using Markdig.Syntax;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

// HL7 Message Enhancement Console Application
// Implements Azure Blob trigger simulation with database enhancement

Console.WriteLine("=== HL7 Message Enhancement System ===");
Console.WriteLine("Starting application...\n");

// Build configuration
var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .Build();

// Setup dependency injection
var services = new ServiceCollection();
ConfigureServices(services, configuration);
var serviceProvider = services.BuildServiceProvider();

// Get logger
var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
logger.LogInformation("HL7 Enhancement System started");

try
{
    // Get main service
    var enhancementService = serviceProvider.GetRequiredService<Hl7EnhancementService>();
    var blobSimulator = serviceProvider.GetRequiredService<BlobTriggerSimulator>();

    // Check database connectivity
    var patientService = serviceProvider.GetRequiredService<PatientDataService>();
    var isDatabaseAvailable = await patientService.IsDatabaseAvailableAsync();

    if (isDatabaseAvailable)
    {
        logger.LogInformation("Database connection successful");
    }
    else
    {
        logger.LogWarning("Database connection failed - will process messages without enhancement");
    }

    // Process mode selection
    Console.WriteLine("Select processing mode:");
    Console.WriteLine("1. Process from Azure Blob Storage");
    Console.WriteLine("2. Process from local directory");
    Console.WriteLine("3. Process from Markdown file (legacy mode)");
    Console.Write("Enter your choice (1-3): ");

    var choice = Console.ReadLine();

    switch (choice)
    {
        case "1":
            await ProcessFromBlobStorageAsync(enhancementService, blobSimulator, logger);
            break;
        case "2":
            await ProcessFromLocalDirectoryAsync(enhancementService, blobSimulator, logger);
            break;
        case "3":
            await ProcessFromMarkdownFileAsync(enhancementService, logger);
            break;
        default:
            Console.WriteLine("Invalid choice. Defaulting to local directory processing.");
            await ProcessFromLocalDirectoryAsync(enhancementService, blobSimulator, logger);
            break;
    }
}
catch (Exception ex)
{
    logger.LogError(ex, "Fatal error in application");
    Console.WriteLine($"Fatal error: {ex.Message}");
}
finally
{
    await serviceProvider.DisposeAsync();
    Console.WriteLine("\nPress any key to exit...");
    Console.ReadKey();
}

// --- Configuration and Service Setup ---
static void ConfigureServices(IServiceCollection services, IConfiguration configuration)
{
    // Add logging
    services.AddLogging(builder =>
    {
        builder.AddConsole();
        builder.AddConfiguration(configuration.GetSection("Logging"));
    });

    // Add configuration
    services.AddSingleton(configuration);

    // Bind HL7 enhancement configuration
    var hl7Config = new Hl7EnhancementConfig();
    configuration.GetSection("Hl7Enhancement").Bind(hl7Config);
    services.AddSingleton(hl7Config);

    // Add Entity Framework
    services.AddDbContext<HmcLiveFeedContext>(options =>
        options.UseSqlServer(configuration.GetConnectionString("HmcLiveFeedsConnectionString")));

    // Add Azure Blob Storage
    var storageConnectionString = configuration.GetValue<string>("Values:AzureWebJobsStorage");
    if (!string.IsNullOrEmpty(storageConnectionString))
    {
        services.AddSingleton(new BlobServiceClient(storageConnectionString));
    }

    // Add application services
    services.AddScoped<PatientDataService>();
    services.AddScoped<Hl7MessageEnhancer>();
    services.AddScoped<Hl7EnhancementService>();

    // Add a file storage service
    services.AddScoped<FileStorageService>(provider =>
    {
        var logger = provider.GetRequiredService<ILogger<FileStorageService>>();
        var config = provider.GetRequiredService<Hl7EnhancementConfig>();
        return new FileStorageService(logger, config.OriginalMessagesPath, config.EnhancedMessagesPath);
    });

    // Add blob trigger simulator
    services.AddScoped<BlobTriggerSimulator>(provider =>
    {
        var blobServiceClient = provider.GetService<BlobServiceClient>();
        var logger = provider.GetRequiredService<ILogger<BlobTriggerSimulator>>();
        var config = provider.GetRequiredService<Hl7EnhancementConfig>();

        if (blobServiceClient != null)
        {
            return new BlobTriggerSimulator(blobServiceClient, logger, config.BlobContainerName);
        }

        // Return a mock implementation if no blob service client
        return new BlobTriggerSimulator(null!, logger, config.BlobContainerName);
    });
}

// --- Processing Methods ---
static async Task ProcessFromBlobStorageAsync(Hl7EnhancementService enhancementService, BlobTriggerSimulator blobSimulator, ILogger logger)
{
    logger.LogInformation("Starting Azure Blob Storage processing");
    Console.WriteLine("\nProcessing HL7 messages from Azure Blob Storage...");

    var processedCount = 0;
    var enhancedCount = 0;
    var failedCount = 0;

    try
    {
        await foreach (var (blobName, content) in blobSimulator.SimulateBlobTriggerAsync())
        {
            Console.WriteLine($"\nProcessing blob: {blobName}");

            var result = await enhancementService.ProcessMessageAsync(blobName, content);

            processedCount++;

            if (result.Status == "Success")
            {
                if (result.WasEnhanced)
                {
                    enhancedCount++;
                    Console.WriteLine($"✓ Enhanced: {blobName} (MRN: {result.ExtractedMrn})");
                }
                else
                {
                    Console.WriteLine($"✓ Processed: {blobName} (MRN: {result.ExtractedMrn}) - No enhancement needed");
                }
            }
            else
            {
                failedCount++;
                Console.WriteLine($"✗ Failed: {blobName} - {result.ErrorMessage}");
            }
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error processing from blob storage");
        Console.WriteLine($"Error: {ex.Message}");
    }

    Console.WriteLine($"\n=== Processing Summary ===");
    Console.WriteLine($"Total processed: {processedCount}");
    Console.WriteLine($"Enhanced: {enhancedCount}");
    Console.WriteLine($"Failed: {failedCount}");
}
static async Task ProcessFromLocalDirectoryAsync(Hl7EnhancementService enhancementService, BlobTriggerSimulator blobSimulator, ILogger logger)
{
    Console.Write("\nEnter the local directory path containing HL7 files: ");
    var directoryPath = Console.ReadLine();

    if (string.IsNullOrEmpty(directoryPath))
    {
        directoryPath = "MDFiles"; // Default to MDFiles directory
        Console.WriteLine($"Using default directory: {directoryPath}");
    }

    logger.LogInformation("Starting local directory processing from: {DirectoryPath}", directoryPath);
    Console.WriteLine($"\nProcessing HL7 messages from directory: {directoryPath}...");

    var processedCount = 0;
    var enhancedCount = 0;
    var failedCount = 0;

    try
    {
        await foreach (var (fileName, content) in blobSimulator.SimulateLocalDirectoryAsync(directoryPath))
        {
            Console.WriteLine($"\nProcessing file: {fileName}");

            var result = await enhancementService.ProcessMessageAsync(fileName, content);

            processedCount++;

            if (result.Status == "Success")
            {
                if (result.WasEnhanced)
                {
                    enhancedCount++;
                    Console.WriteLine($"✓ Enhanced: {fileName} (MRN: {result.ExtractedMrn})");
                }
                else
                {
                    Console.WriteLine($"✓ Processed: {fileName} (MRN: {result.ExtractedMrn}) - No enhancement needed");
                }
            }
            else
            {
                failedCount++;
                Console.WriteLine($"✗ Failed: {fileName} - {result.ErrorMessage}");
            }
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error processing from local directory");
        Console.WriteLine($"Error: {ex.Message}");
    }

    Console.WriteLine($"\n=== Processing Summary ===");
    Console.WriteLine($"Total processed: {processedCount}");
    Console.WriteLine($"Enhanced: {enhancedCount}");
    Console.WriteLine($"Failed: {failedCount}");
}
static async Task ProcessFromMarkdownFileAsync(Hl7EnhancementService enhancementService, ILogger logger)
{
    Console.Write("\nEnter the Markdown file path (or press Enter for default): ");
    var markdownPath = Console.ReadLine();

    if (string.IsNullOrEmpty(markdownPath))
    {
        markdownPath = @"MDFiles\received_hl7_messages_20250601_140004.md";
        Console.WriteLine($"Using default file: {markdownPath}");
    }

    if (!File.Exists(markdownPath))
    {
        Console.WriteLine($"File not found: {markdownPath}");
        return;
    }

    logger.LogInformation("Starting Markdown file processing from: {MarkdownPath}", markdownPath);
    Console.WriteLine($"\nProcessing HL7 messages from Markdown file: {markdownPath}...");

    var markdown = await File.ReadAllTextAsync(markdownPath);
    var pipeline = new MarkdownPipelineBuilder().Build();
    var document = Markdown.Parse(markdown, pipeline);

    // Extract code blocks containing HL7 messages
    var codeBlocks = document.Descendants<CodeBlock>()
        .OfType<FencedCodeBlock>()
        .Where(cb => cb.Info == "hl7" || string.IsNullOrEmpty(cb.Info))
        .ToList();

    Console.WriteLine($"Found {codeBlocks.Count} code blocks in the markdown file.");

    var processedCount = 0;
    var enhancedCount = 0;
    var failedCount = 0;

    var blockIndex = 0;
    foreach (var block in codeBlocks)
    {
        blockIndex++;
        var hl7Raw = block.Lines.ToString();
        var filename = $"markdown_block_{blockIndex}.hl7";

        Console.WriteLine($"\nProcessing block {blockIndex}/{codeBlocks.Count}: {filename}");

        try
        {
            var result = await enhancementService.ProcessMessageAsync(filename, hl7Raw);

            processedCount++;

            if (result.Status == "Success")
            {
                if (result.WasEnhanced)
                {
                    enhancedCount++;
                    Console.WriteLine($"✓ Enhanced: {filename} (MRN: {result.ExtractedMrn})");
                }
                else
                {
                    Console.WriteLine($"✓ Processed: {filename} (MRN: {result.ExtractedMrn}) - No enhancement needed");
                }
            }
            else
            {
                failedCount++;
                Console.WriteLine($"✗ Failed: {filename} - {result.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            failedCount++;
            logger.LogError(ex, "Error processing markdown block {BlockIndex}", blockIndex);
            Console.WriteLine($"✗ Failed: {filename} - {ex.Message}");
        }
    }

    Console.WriteLine($"\n=== Processing Summary ===");
    Console.WriteLine($"Total processed: {processedCount}");
    Console.WriteLine($"Enhanced: {enhancedCount}");
    Console.WriteLine($"Failed: {failedCount}");

    // Legacy: Also generate the extracted fields JSON
    await GenerateExtractedFieldsJsonAsync(codeBlocks, markdownPath, logger);
}

// --- Legacy Helper Methods (for backward compatibility) ---
static async Task GenerateExtractedFieldsJsonAsync(List<FencedCodeBlock> codeBlocks, string markdownPath, ILogger logger)
{
    try
    {
        var extractedList = new List<dynamic>();

        foreach (var block in codeBlocks)
        {
            var hl7Raw = block.Lines.ToString();
            try
            {
                var message = new Message(hl7Raw);
                var isParsed = message.ParseMessage();
                if (isParsed)
                {
                    var extracted = ExtractFieldsFromMessage(message);
                    var messageId = message.GetValue("MSH.10");
                    extractedList.Add(new {
                        messageId,
                        extracted.QATAR_ID_EXP,
                        extracted.HC_EXP_DATE,
                        extracted.FAMILY_PHYSICIAN,
                        extracted.PRIM_ORG_NAME
                    });
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error processing message for JSON extraction");
            }
        }

        var outputPath = Path.ChangeExtension(markdownPath, ".json");
        var extractedJson = JsonSerializer.Serialize(extractedList, new JsonSerializerOptions { WriteIndented = true });
        await File.WriteAllTextAsync(outputPath, extractedJson);
        Console.WriteLine($"Legacy extracted fields JSON saved to: {outputPath}");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error generating extracted fields JSON");
    }
}

static Hl7ExtractedFields ExtractFieldsFromMessage(Message message)
{
    var result = new Hl7ExtractedFields();
    var obxSegments = message.Segments("OBX");
    for (var i = 0; i < obxSegments.Count; i++)
    {
        var identifier = (message.GetValue($"OBX({i+1}).3") ?? "").Replace(" ", "").Replace("_", "").ToUpperInvariant();
        var value = message.GetValue($"OBX({i+1}).5");
        switch (identifier)
        {
            case "QATARIDEXP":
                result.QATAR_ID_EXP = value;
                break;
            case "HCEXPDATE":
                result.HC_EXP_DATE = value;
                break;
            case "FAMILYPHYSICIAN":
                result.FAMILY_PHYSICIAN = value;
                break;
            case "PRIMORGNAME":
                result.PRIM_ORG_NAME = value;
                break;
        }
    }
    return result;
}