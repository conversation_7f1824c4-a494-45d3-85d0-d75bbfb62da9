namespace Hl7Converter;

/// <summary>
/// Result of HL7 message processing operation
/// </summary>
public class Hl7ProcessingResult
{
    /// <summary>
    /// Original filename
    /// </summary>
    public string Filename { get; set; } = string.Empty;

    /// <summary>
    /// Processing status (Success, Failed)
    /// </summary>
    public string Status { get; set; } = string.Empty;

    /// <summary>
    /// Error message if processing failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Extracted MRN from PID.3 segment
    /// </summary>
    public string? ExtractedMrn { get; set; }

    /// <summary>
    /// Fields extracted from an original message
    /// </summary>
    public Hl7ExtractedFields? ExtractedFields { get; set; }

    /// <summary>
    /// Whether the message was enhanced with additional OBX segments
    /// </summary>
    public bool WasEnhanced { get; set; }

    /// <summary>
    /// Path to save an original message file
    /// </summary>
    public string? OriginalFilePath { get; set; }

    /// <summary>
    /// Path to save an enhanced message file
    /// </summary>
    public string? EnhancedFilePath { get; set; }

    /// <summary>
    /// Processing start time
    /// </summary>
    public DateTime ProcessingStartTime { get; set; }

    /// <summary>
    /// Processing end time
    /// </summary>
    public DateTime? ProcessingEndTime { get; set; }

    /// <summary>
    /// Total processing duration
    /// </summary>
    public TimeSpan? ProcessingDuration { get; set; }
}
